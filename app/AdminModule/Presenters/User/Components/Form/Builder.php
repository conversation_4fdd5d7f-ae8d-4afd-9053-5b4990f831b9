<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\User\Components\Form;

use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use stdClass;

final class Builder
{
	public function __construct(
		private readonly CoreBuilder $coreBuilder,
		private readonly StateRepository $stateRepository,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly \App\Model\Security\User $user,
	)
	{
	}

	public function build(Form $form, User $user, User $admin, array $postData): void
	{
		$form->addEmail('email', 'email')->setRequired('email_required')->setDefaultValue($user->email);
		$form->addPassword('password', 'password');
		$form->addPassword('passwordVerify', 'password_verify');

		$states = $this->stateRepository->findAll()->fetchPairs('id', 'name');

		if ($this->user->isInRole(User::ROLE_ADMIN) || $this->user->isInRole(User::ROLE_DEVELOPER)) {
			$roles = User::getConstsByPrefix('ROLE_');
			$form->addSelect('role', 'role', $roles)->setDefaultValue($user->role);
		}

		$priceLevels = $this->priceLevelRepository->findAll()->fetchPairs('id', 'name');
		$form->addSelect('priceLevel', 'price_level_title', $priceLevels);

		$form->addText('firstname', 'user_firstname')->setDefaultValue($user->firstname);
		$form->addText('lastname', 'user_lastname')->setDefaultValue($user->lastname);
		$form->addText('phone', 'phone')->setDefaultValue($user->phone);
		$form->addText('street', 'street');
		$form->addText('city', 'city');
		$form->addText('zip', 'zip');
		$form->addSelect('state', 'state', $states)->setDefaultValue($user->state?->id);
		$form->addText('ic', 'company_id')->setDefaultValue($user->ic);
		$form->addText('dic', 'vat_number')->setDefaultValue($user->dic);
		$form->addText('company', 'company')->setDefaultValue($user->company);

		$this->addCustomAddressesToForm($form, $user, $postData);

		$form->addContainer('setup')->addHidden('cf');

		$this->coreBuilder->addButtons($form);
	}

	private function addCustomAddressesToForm(Form $form, User $user, array $postData): void
	{
		$addressesContainer = $form->addContainer('customAddress');

		if ($postData === []) {
			if ( $user->customAddress !== null) {
				foreach ($user->customAddress as $key => $customAddress) {
					$this->addCustomAddressToForm($addressesContainer, $customAddress, $key);
				}
			}
		} elseif (isset($postData['customAddress'])) {
			foreach ($postData['customAddress'] as $key => $customAddressData) {
				$customAddressData = json_decode(json_encode($customAddressData));
				$this->addCustomAddressToForm($addressesContainer, $customAddressData, $key);
			}
		}
		$this->addCustomAddressToForm($addressesContainer, null, 'newItemMarker');
	}

	private function addCustomAddressToForm(Container $addressesContainer, ?stdClass $address, int|string $addressKey): void
	{
		$addressContainer = $addressesContainer->addContainer($addressKey);
		$addressContainer->addText('addressTitle', 'title')->setDefaultValue($address->addressTitle ?? '');
		$addressContainer->addCheckbox('isDefault', 'is_default_address')->setDefaultValue($address->isDefault ?? false);
		$addressContainer->addText('addressNote', 'note')->setDefaultValue($address->addressNote ?? '');

		$addressContainer->addText('invFirstname', 'first_name')->setDefaultValue($address->invFirstname ?? '');
		$addressContainer->addText('invLastname', 'last_name')->setDefaultValue($address->invLastname ?? '');
		$addressContainer->addText('invPhone', 'phone')->setDefaultValue($address->invPhone ?? '');
		$addressContainer->addText('invCompany', 'company')->setDefaultValue($address->invCompany ?? '');
		$addressContainer->addText('invIc', 'ic')->setDefaultValue($address->invIc ?? '');
		$addressContainer->addText('invDic', 'dic')->setDefaultValue($address->invDic ?? '');
		$addressContainer->addText('invStreet', 'street')->setDefaultValue($address->invStreet ?? '');
		$addressContainer->addText('invCity', 'city')->setDefaultValue($address->invCity ?? '');
		$addressContainer->addText('invZip', 'zip')->setDefaultValue($address->invZip ?? '');

		$addressContainer->addText('delFirstname', 'first_name')->setDefaultValue($address->delFirstname ?? null);
		$addressContainer->addText('delLastname', 'last_name')->setDefaultValue($address->delLastname ?? null);
		$addressContainer->addText('delPhone', 'phone')->setDefaultValue($address->delPhone ?? null);
		$addressContainer->addText('delCompany', 'company')->setDefaultValue($address->delCompany ?? null);
		$addressContainer->addText('delStreet', 'street')->setDefaultValue($address->delStreet ?? null);
		$addressContainer->addText('delCity', 'city')->setDefaultValue($address->delCity ?? null);
		$addressContainer->addText('delZip', 'zip')->setDefaultValue($address->delZip ?? null);
	}

}
