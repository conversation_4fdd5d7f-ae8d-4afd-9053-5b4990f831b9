<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\User\Components\Form;

use App\AdminModule\Presenters\User\Components\Form\FormData\BaseFormData;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Admin\Admin;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use Nette\Security\Passwords;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly UserRepository $userRepository,
		private readonly CustomFields $customFields,
		private readonly Passwords $passwords,
	)
	{
	}

	public function handle(User $user, BaseFormData $data, User $admin): void
	{
		$user->email = $data->email;
		$user->firstname = $data->firstname;
		$user->lastname = $data->lastname;
		if ($data->role !== null) {
			$user->role = $data->role;
		}
		$user->phone = $data->phone;
		$user->ic = $data->ic;
		$user->company = $data->company;
		$user->state = $data->state;
		$user->priceLevel = $data->priceLevel;

		//prepare custom address data
		$newAddressesOrdered = [];
		$newAddresses = $data->customAddress;
		unset($newAddresses['newItemMarker']);
		$newAddressKey = 1;
		foreach ($newAddresses as $key => $customAddress) {
			if (isset($customAddress['isDefault']) && (bool)$customAddress['isDefault']) {
				$newAddressesOrdered[0] = $customAddress;
				unset($newAddresses[$key]);
			}
		}
		foreach ($newAddresses as $customAddress) {
			$newAddressesOrdered[$newAddressKey] = $customAddress;
			$newAddressKey ++;
		}

		$user->customAddress = $newAddressesOrdered;
		$user->editedTime = 'now';

		if ($data->password !== '') {
			$user->password = $this->passwords->hash($data->password);
		}

		if (isset($user->cf)) {
			if (isset($data->setup->cf) && $data->setup->cf !== '') {
				$user->setCf($this->customFields->prepareDataToSave($data->setup->cf));
			} else {
				$user->setCf(new ArrayHash());
			}
		}
		$this->userRepository->persistAndFlush($user);
	}

}
